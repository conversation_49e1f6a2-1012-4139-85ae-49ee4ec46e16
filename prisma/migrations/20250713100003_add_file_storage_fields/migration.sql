-- CreateEnum
CREATE TYPE "UserLevel" AS ENUM ('NEWBIE', 'ACTIVE', 'SENIOR', 'EXPERT', 'MODERATOR', 'ADMIN');

-- CreateEnum
CREATE TYPE "CompanySize" AS ENUM ('STARTUP', 'SMALL', 'MEDIUM', 'LAR<PERSON>', 'ENTERPRISE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PostType" AS ENUM ('DISCUSSION', 'QUESTION', 'SHARING', 'NEWS', 'REVIEW', 'JOB');

-- CreateEnum
CREATE TYPE "InterviewDifficulty" AS ENUM ('EASY', 'MEDIUM', 'HARD', 'VERY_HARD');

-- CreateEnum
CREATE TYPE "InterviewResult" AS ENUM ('PASSED', 'FAILED', 'PENDING', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ReportReason" AS ENUM ('SPAM', 'INAPPROPRIATE', 'FAKE_INFO', 'HARASSMENT', 'COPYRIGHT', 'OTHER');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "ReportStatus" AS ENUM ('PENDING', 'REVIEWING', 'RESOLVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "EmploymentType" AS ENUM ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'FREELANCE');

-- CreateEnum
CREATE TYPE "VerificationStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'REVOKED');

-- CreateEnum
CREATE TYPE "FileCategory" AS ENUM ('CONTRACT', 'CERTIFICATE', 'PHOTO', 'DOCUMENT', 'OTHER');

-- CreateEnum
CREATE TYPE "TargetType" AS ENUM ('WORK_EXPERIENCE', 'EXPERIENCE_FILE', 'SALARY', 'INTERVIEW');

-- CreateEnum
CREATE TYPE "ReviewDecision" AS ENUM ('APPROVED', 'REJECTED', 'REVOKED', 'PENDING_MORE_INFO');

-- CreateEnum
CREATE TYPE "FileFolder" AS ENUM ('AVATARS', 'COMPANY_LOGOS', 'WORK_FILES', 'DOCUMENTS', 'ATTACHMENTS', 'TEMP');

-- CreateEnum
CREATE TYPE "FilePurpose" AS ENUM ('USER_AVATAR', 'COMPANY_LOGO', 'WORK_CERTIFICATE', 'EDUCATION_CERTIFICATE', 'RESUME', 'PORTFOLIO', 'INTERVIEW_ATTACHMENT', 'SALARY_PROOF', 'OTHER');

-- CreateTable
CREATE TABLE "users" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" VARCHAR(255) NOT NULL,
    "emailVerified" TIMESTAMPTZ(6),
    "username" VARCHAR(100),
    "phone" VARCHAR(20),
    "password" VARCHAR(255),
    "name" VARCHAR(100),
    "image" VARCHAR(500),
    "avatar" VARCHAR(500),
    "avatarKey" VARCHAR(200),
    "bio" TEXT,
    "position" VARCHAR(100),
    "company" VARCHAR(200),
    "experience" INTEGER,
    "industry" VARCHAR(100),
    "education" VARCHAR(200),
    "skills" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "level" "UserLevel" DEFAULT 'NEWBIE',
    "points" INTEGER DEFAULT 0,
    "reputation" DECIMAL(10,2) DEFAULT 0.0,
    "storageUsed" BIGINT DEFAULT 0,
    "storageLimit" BIGINT DEFAULT *********,
    "profileCompleteness" INTEGER DEFAULT 0,
    "lastProfileUpdate" TIMESTAMPTZ(6),
    "followersCount" INTEGER DEFAULT 0,
    "followingCount" INTEGER DEFAULT 0,
    "emailVerificationToken" VARCHAR(255),
    "passwordResetToken" VARCHAR(255),
    "passwordResetExpires" TIMESTAMPTZ(6),
    "twoFactorEnabled" BOOLEAN DEFAULT false,
    "twoFactorSecret" VARCHAR(255),
    "isAnonymous" BOOLEAN DEFAULT false,
    "isEmailPublic" BOOLEAN DEFAULT false,
    "isPhonePublic" BOOLEAN DEFAULT false,
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    "isBanned" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "lastLogin" TIMESTAMPTZ(6),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "accounts" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "provider" VARCHAR(50) NOT NULL,
    "providerAccountId" VARCHAR(100) NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" VARCHAR(50),
    "scope" VARCHAR(200),
    "id_token" TEXT,
    "session_state" VARCHAR(200),

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "sessionToken" VARCHAR(255) NOT NULL,
    "userId" UUID NOT NULL,
    "expires" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification_tokens" (
    "identifier" VARCHAR(255) NOT NULL,
    "token" VARCHAR(255) NOT NULL,
    "expires" TIMESTAMPTZ(6) NOT NULL
);

-- CreateTable
CREATE TABLE "companies" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" VARCHAR(200) NOT NULL,
    "nameEn" VARCHAR(200),
    "logo" VARCHAR(500),
    "description" TEXT,
    "website" VARCHAR(500),
    "industry" VARCHAR(100),
    "size" "CompanySize",
    "foundedYear" INTEGER,
    "headquarters" VARCHAR(100),
    "address" TEXT,
    "phone" VARCHAR(50),
    "email" VARCHAR(255),
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    "totalRatings" INTEGER DEFAULT 0,
    "averageRating" DECIMAL(3,2) DEFAULT 0.0,
    "totalSalaries" INTEGER DEFAULT 0,
    "totalReviews" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "posts" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" VARCHAR(200) NOT NULL,
    "content" TEXT NOT NULL,
    "excerpt" VARCHAR(500),
    "type" "PostType" DEFAULT 'DISCUSSION',
    "category" VARCHAR(50),
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "companyId" UUID,
    "authorId" UUID NOT NULL,
    "isAnonymous" BOOLEAN DEFAULT false,
    "isPublished" BOOLEAN DEFAULT true,
    "isPinned" BOOLEAN DEFAULT false,
    "isLocked" BOOLEAN DEFAULT false,
    "isDeleted" BOOLEAN DEFAULT false,
    "viewCount" INTEGER DEFAULT 0,
    "likeCount" INTEGER DEFAULT 0,
    "commentCount" INTEGER DEFAULT 0,
    "shareCount" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "publishedAt" TIMESTAMPTZ(6),

    CONSTRAINT "posts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "comments" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "content" TEXT NOT NULL,
    "postId" UUID NOT NULL,
    "authorId" UUID NOT NULL,
    "parentId" UUID,
    "isAnonymous" BOOLEAN DEFAULT false,
    "isDeleted" BOOLEAN DEFAULT false,
    "likeCount" INTEGER DEFAULT 0,
    "replyCount" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "likes" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "postId" UUID,
    "commentId" UUID,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "likes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bookmarks" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "postId" UUID NOT NULL,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "bookmarks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "salaries" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    "position" VARCHAR(100) NOT NULL,
    "level" VARCHAR(50),
    "department" VARCHAR(100),
    "workLocation" VARCHAR(100),
    "workType" VARCHAR(50),
    "experience" INTEGER,
    "education" VARCHAR(100),
    "baseSalary" DECIMAL(12,2),
    "bonus" DECIMAL(12,2) DEFAULT 0,
    "stockOptions" DECIMAL(12,2) DEFAULT 0,
    "benefits" DECIMAL(12,2) DEFAULT 0,
    "totalSalary" DECIMAL(12,2) NOT NULL,
    "currency" VARCHAR(10) DEFAULT 'CNY',
    "salaryYear" INTEGER NOT NULL,
    "notes" TEXT,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    "isAnonymous" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "salaries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "interviews" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    "position" VARCHAR(100) NOT NULL,
    "department" VARCHAR(100),
    "interviewType" VARCHAR(50),
    "interviewRound" INTEGER DEFAULT 1,
    "interviewDate" DATE,
    "duration" INTEGER,
    "difficulty" "InterviewDifficulty" NOT NULL,
    "result" "InterviewResult" NOT NULL,
    "rating" INTEGER,
    "questions" TEXT[],
    "experience" TEXT,
    "tips" TEXT,
    "notes" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "isAnonymous" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "interviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ratings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    "overallRating" DECIMAL(2,1) NOT NULL,
    "workLifeBalance" INTEGER,
    "compensation" INTEGER,
    "culture" INTEGER,
    "careerGrowth" INTEGER,
    "management" INTEGER,
    "title" VARCHAR(200),
    "pros" TEXT,
    "cons" TEXT,
    "advice" TEXT,
    "isRecommended" BOOLEAN,
    "recommendationReason" TEXT,
    "position" VARCHAR(100),
    "department" VARCHAR(100),
    "workDuration" INTEGER,
    "employmentType" VARCHAR(50),
    "isActive" BOOLEAN DEFAULT true,
    "isAnonymous" BOOLEAN DEFAULT true,
    "isVerified" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reports" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "reporterId" UUID NOT NULL,
    "targetType" VARCHAR(50) NOT NULL,
    "targetId" UUID NOT NULL,
    "reason" "ReportReason" NOT NULL,
    "description" TEXT,
    "status" "ReportStatus" DEFAULT 'PENDING',
    "handlerId" UUID,
    "handlerNotes" TEXT,
    "resolvedAt" TIMESTAMPTZ(6),
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID,

    CONSTRAINT "reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "work_experiences" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "companyName" VARCHAR(200) NOT NULL,
    "position" VARCHAR(100) NOT NULL,
    "department" VARCHAR(100),
    "employmentType" "EmploymentType" NOT NULL,
    "startDate" DATE NOT NULL,
    "endDate" DATE,
    "isCurrent" BOOLEAN DEFAULT false,
    "description" TEXT,
    "achievements" TEXT[],
    "skills" TEXT[],
    "salary" DECIMAL(12,2),
    "currency" VARCHAR(10) DEFAULT 'CNY',
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
    "verifiedById" UUID,
    "verifiedAt" TIMESTAMPTZ(6),
    "isPublic" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "work_experiences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "files" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "originalName" VARCHAR(255) NOT NULL,
    "fileName" VARCHAR(255) NOT NULL,
    "filePath" VARCHAR(500) NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "mimeType" VARCHAR(100) NOT NULL,
    "fileHash" VARCHAR(64) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "description" TEXT,
    "relatedId" UUID,
    "relatedType" VARCHAR(50),
    "uploadedById" UUID NOT NULL,
    "isDeleted" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "experience_files" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "workExperienceId" UUID NOT NULL,
    "fileName" VARCHAR(255) NOT NULL,
    "fileUrl" VARCHAR(500) NOT NULL,
    "fileSize" INTEGER,
    "mimeType" VARCHAR(100),
    "category" "FileCategory" NOT NULL,
    "description" TEXT,
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
    "verifiedById" UUID,
    "verifiedAt" TIMESTAMPTZ(6),
    "uploadedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "experience_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification_records" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "reviewerId" UUID NOT NULL,
    "targetType" "TargetType" NOT NULL,
    "targetId" UUID NOT NULL,
    "decision" "ReviewDecision" NOT NULL,
    "reason" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "verification_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_credibility" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "credibilityScore" DECIMAL(5,2) DEFAULT 0.0,
    "verifiedWorkExperiences" INTEGER DEFAULT 0,
    "verifiedSalaries" INTEGER DEFAULT 0,
    "verifiedInterviews" INTEGER DEFAULT 0,
    "contributionPoints" INTEGER DEFAULT 0,
    "reportCount" INTEGER DEFAULT 0,
    "lastCalculatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_credibility_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file_uploads" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "fileName" VARCHAR(255) NOT NULL,
    "originalName" VARCHAR(255) NOT NULL,
    "fileSize" BIGINT NOT NULL,
    "contentType" VARCHAR(100) NOT NULL,
    "r2Key" VARCHAR(500) NOT NULL,
    "r2Url" VARCHAR(500) NOT NULL,
    "folder" "FileFolder" NOT NULL,
    "purpose" "FilePurpose" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "downloadCount" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "file_uploads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_key" ON "users"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "unique_provider_account" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_sessionToken_key" ON "sessions"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_token_key" ON "verification_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "unique_identifier_token" ON "verification_tokens"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "companies_name_key" ON "companies"("name");

-- CreateIndex
CREATE UNIQUE INDEX "companies_nameEn_key" ON "companies"("nameEn");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_comment_like" ON "likes"("userId", "commentId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_post_like" ON "likes"("userId", "postId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_post_bookmark" ON "bookmarks"("userId", "postId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_company_rating" ON "ratings"("authorId", "companyId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_file_hash_user" ON "files"("fileHash", "uploadedById");

-- CreateIndex
CREATE UNIQUE INDEX "user_credibility_userId_key" ON "user_credibility"("userId");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "posts" ADD CONSTRAINT "posts_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "posts" ADD CONSTRAINT "posts_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comments" ADD CONSTRAINT "comments_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "likes" ADD CONSTRAINT "likes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookmarks" ADD CONSTRAINT "bookmarks_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reports" ADD CONSTRAINT "reports_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "work_experiences" ADD CONSTRAINT "work_experiences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "experience_files" ADD CONSTRAINT "experience_files_workExperienceId_fkey" FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_uploads" ADD CONSTRAINT "file_uploads_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
