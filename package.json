{"name": "workmates", "version": "0.1.0", "private": true, "description": "WorkMates - 职场社区网站", "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "debug:check": "node scripts/debug-setup.js", "debug:dev": "NODE_OPTIONS='--inspect' next dev", "debug:build": "NODE_OPTIONS='--inspect' next build"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/typography": "^0.5.15", "autoprefixer": "^10.4.20", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.451.0", "next": "^15.3.3", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.9.15", "postcss": "^8.4.47", "prisma": "^5.22.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "sharp": "^0.33.5", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.8.7", "@types/nodemailer": "^6.4.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^9.1.0", "node-fetch": "^3.3.2", "prettier": "^3.3.3", "tsx": "^4.19.1", "typescript": "^5.6.3"}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}, "optionalDependencies": {"@next/swc-darwin-arm64": "^15.3.4"}}