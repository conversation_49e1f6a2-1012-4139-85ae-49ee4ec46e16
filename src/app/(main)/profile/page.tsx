'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useProfile } from '@/hooks/useProfile'
import {
  Bookmark,
  Briefcase,
  Calendar,
  Edit,
  Loader2,
  LogOut,
  Mail,
  MessageSquare,
  Settings,
  Star
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useState } from 'react'

/**
 * 个人中心页面
 * 展示用户个人信息、发布的内容、收藏等
 */
export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('posts')
  const { data: session } = useSession()
  const { profile, loading, error, refetch } = useProfile()

  // 如果未登录，重定向到登录页面
  if (!session) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-gray-600 mb-4">请先登录查看个人资料</p>
            <Button asChild>
              <a href="/auth/login">立即登录</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 加载状态
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>加载中...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 错误状态
  if (error || !profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-red-600 mb-4">{error || '获取用户信息失败'}</p>
            <Button onClick={refetch}>重试</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid gap-6 md:grid-cols-3">
        {/* 左侧 - 用户信息卡片 */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <div className="flex flex-col items-center space-y-4">
                {/* 头像 */}
                <Avatar className="h-24 w-24">
                  <AvatarImage src={profile.avatar || session?.user?.image || ''} alt={profile.name || ''} />
                  <AvatarFallback>
                    {(profile.name || profile.email || '?')[0].toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                {/* 用户名和简介 */}
                <div className="text-center">
                  <h2 className="text-2xl font-bold">{profile.name || '未设置姓名'}</h2>
                  <p className="text-sm text-gray-600">
                    @{profile.username || profile.email?.split('@')[0] || 'user'}
                  </p>
                  <p className="mt-2 text-sm text-gray-600">
                    {profile.bio || '这个人很懒，什么都没有留下...'}
                  </p>
                </div>

                {/* 编辑按钮 */}
                <Button variant="outline" className="w-full" asChild>
                  <a href="/profile/settings">
                    <Edit className="mr-2 h-4 w-4" />
                    编辑资料
                  </a>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* 用户信息列表 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span>{profile.email}</span>
                </div>
                {profile.company && profile.position && (
                  <div className="flex items-center gap-2 text-sm">
                    <Briefcase className="h-4 w-4 text-gray-400" />
                    <span>
                      {profile.position} @ {profile.company}
                    </span>
                  </div>
                )}
                {profile.industry && (
                  <div className="flex items-center gap-2 text-sm">
                    <Star className="h-4 w-4 text-gray-400" />
                    <span>{profile.industry}</span>
                  </div>
                )}
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>
                    加入于 {new Date(profile.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="mt-6 grid grid-cols-3 gap-2 text-center">
                <div>
                  <p className="text-2xl font-bold">{profile.stats.postsCount}</p>
                  <p className="text-xs text-gray-600">帖子</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">{profile.stats.commentsCount}</p>
                  <p className="text-xs text-gray-600">评论</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">{profile.stats.totalContributions}</p>
                  <p className="text-xs text-gray-600">贡献</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="mt-6 space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Settings className="mr-2 h-4 w-4" />
                  账户设置
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start text-red-600 hover:text-red-700"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  退出登录
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧 - 内容标签页 */}
        <div className="md:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="posts">
                <MessageSquare className="mr-2 h-4 w-4" />
                帖子
              </TabsTrigger>
              <TabsTrigger value="comments">
                <MessageSquare className="mr-2 h-4 w-4" />
                评论
              </TabsTrigger>
              <TabsTrigger value="likes">
                <Star className="mr-2 h-4 w-4" />
                点赞
              </TabsTrigger>
              <TabsTrigger value="bookmarks">
                <Bookmark className="mr-2 h-4 w-4" />
                收藏
              </TabsTrigger>
            </TabsList>

            {/* 帖子内容 */}
            <TabsContent value="posts" className="space-y-4">
              <PostList />
            </TabsContent>

            {/* 评论内容 */}
            <TabsContent value="comments" className="space-y-4">
              <CommentList />
            </TabsContent>

            {/* 点赞内容 */}
            <TabsContent value="likes" className="space-y-4">
              <div className="text-center py-12 text-gray-600">
                <Star className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>你点赞的内容会显示在这里</p>
              </div>
            </TabsContent>

            {/* 收藏内容 */}
            <TabsContent value="bookmarks" className="space-y-4">
              <div className="text-center py-12 text-gray-600">
                <Bookmark className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>你收藏的内容会显示在这里</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

/**
 * 用户发布的帖子列表
 */
function PostList() {
  const posts = [
    {
      id: 1,
      title: '分享一下我的面试经验',
      content: '最近参加了几家大厂的面试，想和大家分享一下...',
      category: '面经分享',
      likes: 45,
      comments: 12,
      createdAt: '2天前',
    },
    {
      id: 2,
      title: '关于加班文化的一些思考',
      content: '在互联网公司工作了几年，对加班文化有一些自己的看法...',
      category: '职场讨论',
      likes: 89,
      comments: 34,
      createdAt: '1周前',
    },
  ]

  return (
    <div className="space-y-4">
      {posts.map(post => (
        <Card key={post.id}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-lg">{post.title}</CardTitle>
                <CardDescription className="mt-1">
                  {post.content}
                </CardDescription>
              </div>
              <Badge variant="secondary">{post.category}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                <Star className="h-3 w-3" />
                {post.likes}
              </span>
              <span className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                {post.comments}
              </span>
              <span>{post.createdAt}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 用户发布的评论列表
 */
function CommentList() {
  const comments = [
    {
      id: 1,
      postTitle: '大厂裁员潮下，普通程序员该如何应对？',
      content: '我觉得最重要的是保持学习，提升自己的核心竞争力...',
      createdAt: '3小时前',
    },
    {
      id: 2,
      postTitle: '如何优雅地拒绝加班？',
      content: '可以试试和领导沟通，说明自己的工作效率和产出...',
      createdAt: '1天前',
    },
  ]

  return (
    <div className="space-y-4">
      {comments.map(comment => (
        <Card key={comment.id}>
          <CardHeader>
            <CardTitle className="text-sm text-gray-600">
              评论于《{comment.postTitle}》
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{comment.content}</p>
            <p className="mt-2 text-xs text-gray-600">{comment.createdAt}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
