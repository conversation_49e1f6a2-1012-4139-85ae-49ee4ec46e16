'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    AlertCircle,
    Bell,
    Building2,
    CheckCircle,
    Lock,
    Mail,
    MapPin,
    Phone,
    Save,
    Shield,
    Upload,
    User
} from 'lucide-react'
import { useState } from 'react'

/**
 * 用户设置页面
 * 用户个人信息、账户和隐私设置
 */
export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">账户设置</h1>
        <p className="text-gray-600">管理你的个人信息和账户设置</p>
      </div>

      {/* 消息提示 */}
      {message && (
        <Alert 
          variant={message.type === 'error' ? 'destructive' : 'default'} 
          className="mb-6"
        >
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* 设置标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger 
            value="profile" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">个人资料</span>
          </TabsTrigger>
          <TabsTrigger 
            value="security" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <Lock className="h-4 w-4" />
            <span className="hidden sm:inline">账户安全</span>
          </TabsTrigger>
          <TabsTrigger 
            value="notifications" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">通知设置</span>
          </TabsTrigger>
          <TabsTrigger 
            value="privacy" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">隐私设置</span>
          </TabsTrigger>
        </TabsList>

        {/* 个人信息 */}
        <TabsContent value="profile">
          <ProfileSettings isLoading={isLoading} setIsLoading={setIsLoading} setMessage={setMessage} />
        </TabsContent>

        {/* 账户安全 */}
        <TabsContent value="security">
          <AccountSettings isLoading={isLoading} setIsLoading={setIsLoading} setMessage={setMessage} />
        </TabsContent>

        {/* 通知设置 */}
        <TabsContent value="notifications">
          <NotificationSettings isLoading={isLoading} setIsLoading={setIsLoading} setMessage={setMessage} />
        </TabsContent>

        {/* 隐私设置 */}
        <TabsContent value="privacy">
          <PrivacySettings isLoading={isLoading} setIsLoading={setIsLoading} setMessage={setMessage} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 个人信息设置组件
 */
function ProfileSettings({ 
  isLoading, 
  setIsLoading, 
  setMessage 
}: {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  setMessage: (message: { type: 'success' | 'error', text: string } | null) => void
}) {
  const [profile, setProfile] = useState({
    avatar: '',
    name: '',
    username: '',
    email: '',
    phone: '',
    bio: '',
    location: '',
    company: '',
    position: '',
    experience: '',
    skills: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    try {
      // TODO: 调用更新个人信息 API
      console.log('更新个人信息:', profile)
      
      setTimeout(() => {
        setMessage({ type: 'success', text: '个人信息更新成功' })
        setIsLoading(false)
      }, 1000)
    } catch {
      setMessage({ type: 'error', text: '更新失败，请稍后重试' })
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-6 md:grid-cols-3">
        {/* 头像设置 */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>头像</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            <Avatar className="h-32 w-32">
              <AvatarImage src={profile.avatar} alt={profile.name} />
              <AvatarFallback>{profile.name[0]}</AvatarFallback>
            </Avatar>
            <Button variant="outline" type="button">
              <Upload className="mr-2 h-4 w-4" />
              上传头像
            </Button>
            <p className="text-xs text-gray-500 text-center">
              支持 JPG、PNG 格式，大小不超过 2MB
            </p>
          </CardContent>
        </Card>

        {/* 基本信息 */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input
                  id="name"
                  value={profile.name}
                  onChange={(e) => setProfile(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  value={profile.username}
                  onChange={(e) => setProfile(prev => ({ ...prev, username: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">个人简介</Label>
              <textarea
                id="bio"
                value={profile.bio}
                onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                className="w-full h-24 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="简单介绍一下自己..."
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="location">所在地</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="location"
                    className="pl-10"
                    value={profile.location}
                    onChange={(e) => setProfile(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="experience">工作经验</Label>
                <Select 
                  value={profile.experience} 
                  onValueChange={(value) => setProfile(prev => ({ ...prev, experience: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1年以下">1年以下</SelectItem>
                    <SelectItem value="1-3年">1-3年</SelectItem>
                    <SelectItem value="3-5年">3-5年</SelectItem>
                    <SelectItem value="5-10年">5-10年</SelectItem>
                    <SelectItem value="10年以上">10年以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="company">公司</Label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="company"
                    className="pl-10"
                    value={profile.company}
                    onChange={(e) => setProfile(prev => ({ ...prev, company: e.target.value }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">职位</Label>
                <Input
                  id="position"
                  value={profile.position}
                  onChange={(e) => setProfile(prev => ({ ...prev, position: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="skills">技能标签</Label>
              <Input
                id="skills"
                value={profile.skills}
                onChange={(e) => setProfile(prev => ({ ...prev, skills: e.target.value }))}
                placeholder="用逗号分隔，如：前端开发, React, TypeScript"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6 flex justify-end">
        <Button type="submit" disabled={isLoading}>
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? '保存中...' : '保存更改'}
        </Button>
      </div>
    </form>
  )
}

/**
 * 账户安全设置组件
 */
function AccountSettings({
  isLoading,
  setIsLoading,
  setMessage
}: {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  setMessage: (message: { type: 'success' | 'error', text: string } | null) => void
}) {
  const [passwords, setPasswords] = useState({
    current: '',
    new: '',
    confirm: '',
  })

  const [contact] = useState({
    email: '<EMAIL>',
    phone: '138****8888',
    emailVerified: true,
    phoneVerified: false,
  })

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (passwords.new !== passwords.confirm) {
      setMessage({ type: 'error', text: '新密码和确认密码不一致' })
      return
    }

    setIsLoading(true)
    setMessage(null)

    try {
      // TODO: 调用修改密码 API
      console.log('修改密码')
      
      setTimeout(() => {
        setMessage({ type: 'success', text: '密码修改成功' })
        setPasswords({ current: '', new: '', confirm: '' })
        setIsLoading(false)
      }, 1000)
    } catch {
      setMessage({ type: 'error', text: '密码修改失败' })
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 修改密码 */}
      <Card>
        <CardHeader>
          <CardTitle>修改密码</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handlePasswordChange} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="current-password">当前密码</Label>
              <Input
                id="current-password"
                type="password"
                value={passwords.current}
                onChange={(e) => setPasswords(prev => ({ ...prev, current: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-password">新密码</Label>
              <Input
                id="new-password"
                type="password"
                value={passwords.new}
                onChange={(e) => setPasswords(prev => ({ ...prev, new: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-password">确认新密码</Label>
              <Input
                id="confirm-password"
                type="password"
                value={passwords.confirm}
                onChange={(e) => setPasswords(prev => ({ ...prev, confirm: e.target.value }))}
                required
              />
            </div>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? '修改中...' : '修改密码'}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* 联系方式验证 */}
      <Card>
        <CardHeader>
          <CardTitle>联系方式验证</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-gray-400" />
              <div>
                <p className="font-medium">{contact.email}</p>
                <p className="text-sm text-gray-600">
                  {contact.emailVerified ? '已验证' : '未验证'}
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              {contact.emailVerified ? '更换邮箱' : '验证邮箱'}
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-gray-400" />
              <div>
                <p className="font-medium">{contact.phone}</p>
                <p className="text-sm text-gray-600">
                  {contact.phoneVerified ? '已验证' : '未验证'}
                </p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              {contact.phoneVerified ? '更换手机' : '验证手机'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 两步验证 */}
      <Card>
        <CardHeader>
          <CardTitle>两步验证</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">启用两步验证</p>
              <p className="text-sm text-gray-600">为你的账户添加额外的安全保护</p>
            </div>
            <Button variant="outline">
              启用
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 通知设置组件
 */
function NotificationSettings({
  isLoading,
  setIsLoading,
  setMessage
}: {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  setMessage: (message: { type: 'success' | 'error', text: string } | null) => void
}) {
  const [notifications, setNotifications] = useState({
    email: {
      comments: true,
      likes: true,
      follows: true,
      mentions: true,
      newsletter: false,
    },
    push: {
      comments: true,
      likes: false,
      follows: true,
      mentions: true,
      updates: false,
    },
    sms: {
      security: true,
      important: false,
    }
  })

  const handleSave = async () => {
    setIsLoading(true)
    setMessage(null)

    try {
      // TODO: 调用保存通知设置 API
      console.log('保存通知设置:', notifications)
      
      setTimeout(() => {
        setMessage({ type: 'success', text: '通知设置已保存' })
        setIsLoading(false)
      }, 1000)
    } catch {
      setMessage({ type: 'error', text: '保存失败，请稍后重试' })
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 邮件通知 */}
      <Card>
        <CardHeader>
          <CardTitle>邮件通知</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            comments: '新评论',
            likes: '点赞',
            follows: '新关注者',
            mentions: '提及我的',
            newsletter: '产品更新和新闻',
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={`email-${key}`} className="cursor-pointer">
                {label}
              </Label>
              <Checkbox
                id={`email-${key}`}
                checked={notifications.email[key as keyof typeof notifications.email]}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({
                    ...prev,
                    email: { ...prev.email, [key]: checked }
                  }))
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 推送通知 */}
      <Card>
        <CardHeader>
          <CardTitle>推送通知</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            comments: '新评论',
            likes: '点赞',
            follows: '新关注者',
            mentions: '提及我的',
            updates: '系统更新',
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={`push-${key}`} className="cursor-pointer">
                {label}
              </Label>
              <Checkbox
                id={`push-${key}`}
                checked={notifications.push[key as keyof typeof notifications.push]}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({
                    ...prev,
                    push: { ...prev.push, [key]: checked }
                  }))
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 短信通知 */}
      <Card>
        <CardHeader>
          <CardTitle>短信通知</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            security: '安全提醒',
            important: '重要通知',
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={`sms-${key}`} className="cursor-pointer">
                {label}
              </Label>
              <Checkbox
                id={`sms-${key}`}
                checked={notifications.sms[key as keyof typeof notifications.sms]}
                onCheckedChange={(checked) => 
                  setNotifications(prev => ({
                    ...prev,
                    sms: { ...prev.sms, [key]: checked }
                  }))
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? '保存中...' : '保存设置'}
        </Button>
      </div>
    </div>
  )
}

/**
 * 隐私设置组件
 */
function PrivacySettings({
  isLoading,
  setIsLoading,
  setMessage
}: {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  setMessage: (message: { type: 'success' | 'error', text: string } | null) => void
}) {
  const [privacy, setPrivacy] = useState({
    profilePublic: true,
    showEmail: false,
    showPhone: false,
    allowMessages: true,
    showOnline: true,
    dataCollection: false,
    marketingEmails: false,
  })

  const handleSave = async () => {
    setIsLoading(true)
    setMessage(null)

    try {
      // TODO: 调用保存隐私设置 API
      console.log('保存隐私设置:', privacy)
      
      setTimeout(() => {
        setMessage({ type: 'success', text: '隐私设置已保存' })
        setIsLoading(false)
      }, 1000)
    } catch {
      setMessage({ type: 'error', text: '保存失败，请稍后重试' })
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 个人资料可见性 */}
      <Card>
        <CardHeader>
          <CardTitle>个人资料可见性</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            profilePublic: '公开个人资料',
            showEmail: '显示邮箱地址',
            showPhone: '显示手机号码',
            allowMessages: '允许他人发送私信',
            showOnline: '显示在线状态',
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={`privacy-${key}`} className="cursor-pointer">
                {label}
              </Label>
              <Checkbox
                id={`privacy-${key}`}
                checked={privacy[key as keyof typeof privacy]}
                onCheckedChange={(checked) => 
                  setPrivacy(prev => ({ ...prev, [key]: checked }))
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 数据使用 */}
      <Card>
        <CardHeader>
          <CardTitle>数据使用</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            dataCollection: '允许收集使用数据用于改进服务',
            marketingEmails: '接收营销邮件',
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={`data-${key}`} className="cursor-pointer">
                {label}
              </Label>
              <Checkbox
                id={`data-${key}`}
                checked={privacy[key as keyof typeof privacy]}
                onCheckedChange={(checked) => 
                  setPrivacy(prev => ({ ...prev, [key]: checked }))
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 数据导出和删除 */}
      <Card>
        <CardHeader>
          <CardTitle>数据管理</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">导出数据</p>
              <p className="text-sm text-gray-600">下载你的所有个人数据</p>
            </div>
            <Button variant="outline">
              导出数据
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-red-600">删除账户</p>
              <p className="text-sm text-gray-600">永久删除你的账户和所有数据</p>
            </div>
            <Button variant="destructive">
              删除账户
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? '保存中...' : '保存设置'}
        </Button>
      </div>
    </div>
  )
} 