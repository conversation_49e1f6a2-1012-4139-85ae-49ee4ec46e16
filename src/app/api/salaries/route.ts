import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建薪资数据的验证模式
const createSalarySchema = z.object({
  company: z.string().min(1, '公司名称不能为空'),
  position: z.string().min(1, '职位不能为空'),
  level: z.string().optional(),
  department: z.string().optional(),
  location: z.string().optional(),
  workYears: z.string().optional(),
  companyYears: z.string().optional(),
  baseSalary: z.string().min(1, '基本薪资不能为空'),
  bonus: z.string().optional(),
  stockValue: z.string().optional(),
  allowances: z.string().optional(),
  otherBenefits: z.string().optional(),
  workHours: z.string().optional(),
  workIntensity: z.string().optional(),
  benefits: z.array(z.string()).default([]),
  description: z.string().optional(),
  isAnonymous: z.boolean().default(true),
})

/**
 * 提交薪资数据
 * POST /api/salaries
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再提交薪资信息',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = createSalarySchema.parse(body)

    // 查找或创建公司
    let company = await prisma.company.findFirst({
      where: {
        name: {
          equals: validatedData.company,
          mode: 'insensitive',
        },
      },
    })

    if (!company) {
      // 如果公司不存在，创建新公司
      company = await prisma.company.create({
        data: {
          name: validatedData.company,
          isActive: true,
        },
      })
    }

    // 计算总薪资
    const baseSalary = parseInt(validatedData.baseSalary) || 0
    const bonus = parseInt(validatedData.bonus || '0') || 0
    const stockValue = parseInt(validatedData.stockValue || '0') || 0
    const allowances = parseInt(validatedData.allowances || '0') || 0
    const totalSalary = baseSalary + bonus + stockValue + allowances

    // 解析工作年限
    const experience = parseInt(validatedData.workYears || '0') || 0

    // 创建薪资记录
    const salary = await prisma.salary.create({
      data: {
        position: validatedData.position,
        level: validatedData.level,
        workLocation: validatedData.location,
        workType: 'FULL_TIME', // 默认全职
        experience,
        baseSalary,
        bonus,
        totalSalary,
        currency: 'CNY', // 默认人民币
        notes: validatedData.description,
        tags: validatedData.benefits,
        isVerified: false, // 新提交的数据默认未验证
        authorId: session.user.id,
        companyId: company.id,
      },
      select: {
        id: true,
        position: true,
        level: true,
        totalSalary: true,
        currency: true,
        createdAt: true,
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json(
      {
        success: true,
        message: '薪资信息提交成功',
        data: salary,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('提交薪资数据失败:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入数据格式',
            details: error.errors,
          },
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        message: '提交失败',
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
