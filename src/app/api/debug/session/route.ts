import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextResponse } from 'next/server'

/**
 * 调试Session API
 * GET /api/debug/session
 */
export async function GET() {
  try {
    console.log('🔍 开始调试session...')
    
    // 获取session
    const session = await auth()
    console.log('📋 Session内容:', JSON.stringify(session, null, 2))

    if (!session) {
      return NextResponse.json({
        success: true,
        message: '无session',
        data: {
          hasSession: false,
          session: null,
        },
      })
    }

    if (!session.user) {
      return NextResponse.json({
        success: true,
        message: 'Session存在但无用户信息',
        data: {
          hasSession: true,
          hasUser: false,
          session,
        },
      })
    }

    // 检查用户是否在数据库中存在
    let userExists = false
    let dbUser = null
    
    if (session.user.id) {
      console.log('🔍 查找用户ID:', session.user.id)
      
      try {
        dbUser = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: {
            id: true,
            email: true,
            name: true,
            createdAt: true,
          },
        })
        
        userExists = !!dbUser
        console.log('👤 数据库用户:', dbUser)
      } catch (error) {
        console.error('❌ 查找用户时出错:', error)
      }
    }

    // 获取所有用户列表用于对比
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
      },
      take: 10,
    })

    return NextResponse.json({
      success: true,
      message: '调试信息',
      data: {
        hasSession: true,
        hasUser: true,
        session: {
          user: session.user,
          expires: session.expires,
        },
        database: {
          userExists,
          dbUser,
          allUsers,
          totalUsers: allUsers.length,
        },
        analysis: {
          sessionUserId: session.user.id,
          sessionUserEmail: session.user.email,
          sessionUserName: session.user.name,
          userExistsInDb: userExists,
          possibleIssue: !userExists ? 'Session中的用户ID在数据库中不存在' : '用户存在，可能是其他问题',
        },
      },
    })
  } catch (error) {
    console.error('❌ 调试session时出错:', error)
    return NextResponse.json(
      {
        success: false,
        message: '调试失败',
        error: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    )
  }
}
