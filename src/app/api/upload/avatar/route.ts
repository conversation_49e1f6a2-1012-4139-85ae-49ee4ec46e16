import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

// 头像配置
const AVATAR_CONFIG = {
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  maxSize: 5 * 1024 * 1024, // 5MB
  folder: 'avatars' as const,
  dimensions: { width: 200, height: 200 }, // 建议尺寸
}

/**
 * 用户头像上传API
 * POST /api/upload/avatar
 *
 * Body (FormData):
 * - file: File - 头像图片文件
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再上传头像',
          },
        },
        { status: 401 }
      )
    }

    // 解析 FormData
    const formData = await request.formData()
    const file = formData.get('file') as File

    // 验证文件是否存在
    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: '未选择文件',
          error: {
            code: 'NO_FILE_PROVIDED',
            message: '请选择要上传的头像文件',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!AVATAR_CONFIG.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: '文件类型不支持',
          error: {
            code: 'INVALID_FILE_TYPE',
            message: `支持的格式: ${AVATAR_CONFIG.allowedTypes.join(', ')}`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > AVATAR_CONFIG.maxSize) {
      return NextResponse.json(
        {
          success: false,
          message: '文件过大',
          error: {
            code: 'FILE_TOO_LARGE',
            message: `文件大小不能超过 ${Math.round(AVATAR_CONFIG.maxSize / 1024 / 1024)}MB`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件名长度
    if (file.name.length > 255) {
      return NextResponse.json(
        {
          success: false,
          message: '文件名过长',
          error: {
            code: 'FILENAME_TOO_LONG',
            message: '文件名不能超过255个字符',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    if (buffer.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '文件内容为空',
          error: {
            code: 'EMPTY_FILE',
            message: '请选择有效的图片文件',
          },
        },
        { status: 400 }
      )
    }

    // 验证图片文件头
    const isValidImage = validateImageHeader(new Uint8Array(buffer))
    if (!isValidImage) {
      return NextResponse.json(
        {
          success: false,
          message: '无效的图片文件',
          error: {
            code: 'INVALID_IMAGE',
            message: '文件不是有效的图片格式',
          },
        },
        { status: 400 }
      )
    }

    // 生成文件名和路径
    const fileExtension = path.extname(file.name)
    const fileName = `${session.user.id}-${Date.now()}${fileExtension}`
    const uploadDir = path.join(
      process.cwd(),
      'public',
      'uploads',
      AVATAR_CONFIG.directory
    )
    const filePath = path.join(uploadDir, fileName)
    const publicUrl = `/uploads/${AVATAR_CONFIG.directory}/${fileName}`

    // 确保上传目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 删除用户之前的头像文件（如果存在）
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { avatar: true },
    })

    if (
      currentUser?.avatar &&
      currentUser.avatar.startsWith('/uploads/avatars/')
    ) {
      try {
        const oldFilePath = path.join(
          process.cwd(),
          'public',
          currentUser.avatar
        )
        if (existsSync(oldFilePath)) {
          const fs = require('fs')
          fs.unlinkSync(oldFilePath)
        }
      } catch (error) {
        console.warn('删除旧头像文件失败:', error)
        // 不影响新头像上传
      }
    }

    // 保存文件到磁盘
    await writeFile(filePath, buffer)

    // 更新用户头像URL到数据库
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        avatar: publicUrl,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        updatedAt: true,
      },
    })

    // 更新用户积分（上传头像奖励）
    await updateUserPoints(session.user.id, 'AVATAR_UPLOADED', 10)

    return NextResponse.json(
      {
        success: true,
        message: '头像上传成功',
        data: {
          user: updatedUser,
          avatar: {
            url: publicUrl,
            fileName,
            size: file.size,
            type: file.type,
            uploadedAt: new Date().toISOString(),
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('头像上传失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '头像上传失败',
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 获取当前用户头像信息
 * GET /api/upload/avatar
 */
export async function GET() {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再查看头像信息',
          },
        },
        { status: 401 }
      )
    }

    // 获取用户头像信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        avatar: true,
        updatedAt: true,
      },
    })

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: '用户不存在',
          error: {
            code: 'USER_NOT_FOUND',
            message: '指定的用户不存在',
          },
        },
        { status: 404 }
      )
    }

    // 检查头像文件是否存在
    let avatarExists = false
    if (user.avatar && user.avatar.startsWith('/uploads/avatars/')) {
      const avatarPath = path.join(process.cwd(), 'public', user.avatar)
      avatarExists = existsSync(avatarPath)
    }

    return NextResponse.json({
      success: true,
      message: '获取头像信息成功',
      data: {
        avatar: user.avatar,
        hasAvatar: !!user.avatar,
        avatarExists,
        config: {
          allowedTypes: AVATAR_CONFIG.allowedTypes,
          maxSize: AVATAR_CONFIG.maxSize,
          recommendedDimensions: AVATAR_CONFIG.dimensions,
        },
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取头像信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取头像信息失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 删除用户头像
 * DELETE /api/upload/avatar
 */
export async function DELETE() {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再删除头像',
          },
        },
        { status: 401 }
      )
    }

    // 获取当前用户头像信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { avatar: true },
    })

    if (!user?.avatar) {
      return NextResponse.json(
        {
          success: false,
          message: '没有头像可删除',
          error: {
            code: 'NO_AVATAR_TO_DELETE',
            message: '用户当前没有设置头像',
          },
        },
        { status: 400 }
      )
    }

    // 删除头像文件
    if (user.avatar.startsWith('/uploads/avatars/')) {
      try {
        const avatarPath = path.join(process.cwd(), 'public', user.avatar)
        if (existsSync(avatarPath)) {
          const fs = require('fs')
          fs.unlinkSync(avatarPath)
        }
      } catch (error) {
        console.warn('删除头像文件失败:', error)
        // 继续执行数据库更新
      }
    }

    // 清除数据库中的头像URL
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        avatar: null,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      message: '头像删除成功',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除头像失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除头像失败',
        error: {
          code: 'DELETE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 验证图片文件头
 */
function validateImageHeader(bytes: Uint8Array): boolean {
  // JPEG: FF D8 FF
  if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
    return true
  }

  // PNG: 89 50 4E 47 0D 0A 1A 0A
  if (
    bytes[0] === 0x89 &&
    bytes[1] === 0x50 &&
    bytes[2] === 0x4e &&
    bytes[3] === 0x47
  ) {
    return true
  }

  // WebP: RIFF ... WEBP
  if (
    bytes[0] === 0x52 &&
    bytes[1] === 0x49 &&
    bytes[2] === 0x46 &&
    bytes[3] === 0x46 &&
    bytes[8] === 0x57 &&
    bytes[9] === 0x45 &&
    bytes[10] === 0x42 &&
    bytes[11] === 0x50
  ) {
    return true
  }

  // GIF: GIF87a or GIF89a
  if (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46) {
    return true
  }

  return false
}

/**
 * 更新用户积分
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
