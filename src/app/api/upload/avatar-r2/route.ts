import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { uploadToR2, deleteFromR2, extractKeyFromUrl, validateFile } from '@/lib/r2-upload'

/**
 * 用户头像上传API (使用 Cloudflare R2)
 * POST /api/upload/avatar-r2
 *
 * Body (FormData):
 * - file: File - 头像图片文件
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再上传头像',
          },
        },
        { status: 401 }
      )
    }

    // 解析 FormData
    const formData = await request.formData()
    const file = formData.get('file') as File

    // 验证文件是否存在
    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: '未选择文件',
          error: {
            code: 'NO_FILE_PROVIDED',
            message: '请选择要上传的头像文件',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件类型和大小
    const validation = validateFile(file, 'avatar')
    if (!validation.valid) {
      return NextResponse.json(
        {
          success: false,
          message: validation.error,
          error: {
            code: 'INVALID_FILE',
            message: validation.error,
          },
        },
        { status: 400 }
      )
    }

    // 转换为 Buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // 删除用户之前的头像文件（如果存在）
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { avatar: true },
    })

    let oldKey: string | null = null
    if (currentUser?.avatar) {
      oldKey = extractKeyFromUrl(currentUser.avatar)
    }

    // 上传到 R2
    const uploadResult = await uploadToR2({
      file: buffer,
      fileName: file.name,
      contentType: file.type,
      folder: 'avatars',
      userId: session.user.id,
    })

    // 更新数据库
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        avatar: uploadResult.url,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        updatedAt: true,
      },
    })

    // 删除旧头像文件
    if (oldKey) {
      try {
        await deleteFromR2(oldKey)
      } catch (error) {
        console.warn('删除旧头像文件失败:', error)
        // 不影响新头像上传
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: '头像上传成功',
        data: {
          user: updatedUser,
          upload: uploadResult,
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('头像上传失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '上传失败',
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
