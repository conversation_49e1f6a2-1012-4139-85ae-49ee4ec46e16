import { PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import r2Client from './r2-client'
import crypto from 'crypto'

export interface UploadOptions {
  file: Buffer
  fileName: string
  contentType: string
  folder: 'avatars' | 'company-logos' | 'work-files' | 'documents'
  userId?: string
}

export interface UploadResult {
  success: boolean
  url: string
  key: string
  fileName: string
}

/**
 * 上传文件到 Cloudflare R2
 */
export async function uploadToR2({
  file,
  fileName,
  contentType,
  folder,
  userId
}: UploadOptions): Promise<UploadResult> {
  // 生成唯一文件名
  const timestamp = Date.now()
  const hash = crypto.randomBytes(8).toString('hex')
  const extension = fileName.split('.').pop()
  const uniqueFileName = userId 
    ? `${userId}-${timestamp}-${hash}.${extension}`
    : `${timestamp}-${hash}.${extension}`
  
  const key = `${folder}/${uniqueFileName}`
  
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME!,
    Key: key,
    Body: file,
    ContentType: contentType,
    Metadata: {
      originalName: fileName,
      uploadedBy: userId || 'anonymous',
      uploadedAt: new Date().toISOString(),
    },
  })

  try {
    await r2Client.send(command)
    
    // 返回公共访问 URL
    const publicUrl = process.env.R2_CUSTOM_DOMAIN 
      ? `${process.env.R2_CUSTOM_DOMAIN}/${key}`
      : `${process.env.R2_PUBLIC_URL}/${key}`
    
    return {
      success: true,
      url: publicUrl,
      key,
      fileName: uniqueFileName,
    }
  } catch (error) {
    console.error('R2 上传失败:', error)
    throw new Error('文件上传失败')
  }
}

/**
 * 从 R2 删除文件
 */
export async function deleteFromR2(key: string): Promise<boolean> {
  const command = new DeleteObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME!,
    Key: key,
  })

  try {
    await r2Client.send(command)
    return true
  } catch (error) {
    console.error('R2 删除失败:', error)
    return false
  }
}

/**
 * 从 URL 提取 R2 key
 */
export function extractKeyFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    // 移除开头的 '/'
    return urlObj.pathname.substring(1)
  } catch {
    return null
  }
}

// 文件大小限制
export const FILE_SIZE_LIMITS = {
  avatar: 5 * 1024 * 1024,      // 5MB
  document: 20 * 1024 * 1024,   // 20MB
  workFile: 10 * 1024 * 1024,   // 10MB
  companyLogo: 5 * 1024 * 1024, // 5MB
}

// 文件类型限制
export const ALLOWED_FILE_TYPES = {
  avatar: ['image/jpeg', 'image/png', 'image/webp'],
  document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  workFile: ['image/jpeg', 'image/png', 'application/pdf'],
  companyLogo: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
}

/**
 * 验证文件类型和大小
 */
export function validateFile(
  file: File, 
  type: keyof typeof FILE_SIZE_LIMITS
): { valid: boolean; error?: string } {
  // 检查文件大小
  if (file.size > FILE_SIZE_LIMITS[type]) {
    return {
      valid: false,
      error: `文件大小不能超过 ${FILE_SIZE_LIMITS[type] / (1024 * 1024)}MB`
    }
  }

  // 检查文件类型
  if (!ALLOWED_FILE_TYPES[type].includes(file.type)) {
    return {
      valid: false,
      error: `不支持的文件类型，仅支持: ${ALLOWED_FILE_TYPES[type].join(', ')}`
    }
  }

  return { valid: true }
}
