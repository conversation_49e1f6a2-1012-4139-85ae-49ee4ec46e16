import { auth } from '@/lib/auth'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

/**
 * 中间件配置
 * 用于保护需要认证的路由
 */
export async function middleware(request: NextRequest) {
  const session = await auth()



  // 需要保护的路由前缀
  const protectedPaths = [
    '/profile',
    '/companies/submit',
    '/companies/*/reviews/submit',
    '/companies/salaries/submit',
    '/companies/interviews/submit',
    '/forum/create',
  ]

  // 检查当前路径是否需要保护
  const isProtectedPath = protectedPaths.some(path => {
    const regex = new RegExp(`^${path.replace('*', '.*')}`)
    return regex.test(request.nextUrl.pathname)
  })

  // 如果是受保护的路径且用户未登录，重定向到登录页
  if (isProtectedPath && !session) {
    const url = new URL('/auth/login', request.url)
    url.searchParams.set('callbackUrl', request.nextUrl.pathname)
    return NextResponse.redirect(url)
  }

  // 如果用户已登录且访问认证页面，重定向到首页
  // 更严格的检查：确保session存在且有用户信息
  if (session?.user && (
    request.nextUrl.pathname.startsWith('/auth/login') ||
    request.nextUrl.pathname.startsWith('/auth/register')
  )) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  return NextResponse.next()
}

// 配置中间件应用的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了：
     * 1. /api (API routes)
     * 2. /_next (Next.js internals)
     * 3. /_static (静态文件)
     * 4. /favicon.ico, /sitemap.xml (静态资源)
     */
    '/((?!api|_next|_static|favicon.ico|sitemap.xml).*)',
  ],
} 