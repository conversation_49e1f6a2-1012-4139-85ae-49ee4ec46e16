const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function createCompanyTestData() {
  console.log('🏢 创建企业测试数据...\n')

  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 获取第一个企业和多个用户
    const company = await prisma.company.findFirst({
      where: { isActive: true },
    })

    const users = await prisma.user.findMany({
      take: 3, // 获取3个用户
    })

    if (!company) {
      console.log('❌ 没有找到企业数据')
      return
    }

    if (users.length === 0) {
      console.log('❌ 没有找到用户数据')
      return
    }

    console.log(`📊 为企业 "${company.name}" 创建测试数据...`)
    console.log(`👤 使用 ${users.length} 个用户`)

    // 先清理该企业的现有测试数据
    console.log('\n🧹 清理现有测试数据...')
    await prisma.rating.deleteMany({
      where: { companyId: company.id }
    })
    await prisma.salary.deleteMany({
      where: { companyId: company.id }
    })
    await prisma.interview.deleteMany({
      where: { companyId: company.id }
    })
    console.log('✅ 清理完成')

    // 1. 创建企业评价数据
    console.log('\n1. 创建企业评价...')
    const ratings = [
      {
        title: '工作环境不错，团队氛围很好',
        position: '前端工程师',
        department: '技术部',
        workDuration: 24, // 2年 = 24个月
        employmentType: 'FULL_TIME',
        overallRating: 4.5,
        workLifeBalance: 4.0,
        compensation: 4.5,
        culture: 5.0,
        careerGrowth: 4.0,
        management: 4.5,
        pros: '技术栈新，学习机会多，同事都很友善，办公环境舒适',
        cons: '有时候项目比较紧急，需要加班',
        advice: '建议提前了解团队文化，准备好学习新技术',
        isRecommended: true,
        recommendationReason: '整体工作体验很好，适合技术成长',
        isAnonymous: true,
        isVerified: false,
        authorId: users[0].id,
        companyId: company.id,
      },
      {
        title: '薪资待遇不错，但工作压力较大',
        position: '产品经理',
        department: '产品部',
        workDuration: 18, // 1年半 = 18个月
        employmentType: 'FULL_TIME',
        overallRating: 3.5,
        workLifeBalance: 3.0,
        compensation: 4.5,
        culture: 4.0,
        careerGrowth: 3.5,
        management: 3.5,
        pros: '薪资有竞争力，产品线丰富，有机会接触不同业务',
        cons: '工作节奏快，经常需要加班，压力比较大',
        advice: '需要有较强的抗压能力和学习能力',
        isRecommended: true,
        recommendationReason: '适合想要快速成长的人',
        isAnonymous: true,
        isVerified: false,
        authorId: users[1] ? users[1].id : users[0].id,
        companyId: company.id,
      }
    ]

    for (const rating of ratings) {
      await prisma.rating.create({ data: rating })
      console.log(`  ✅ 创建评价: ${rating.title}`)
    }

    // 2. 创建薪资数据
    console.log('\n2. 创建薪资数据...')
    const salaries = [
      {
        position: '前端工程师',
        level: 'P6',
        department: '技术部',
        workLocation: '北京',
        workType: 'FULL_TIME',
        experience: 3,
        education: 'BACHELOR',
        baseSalary: 300000,
        bonus: 60000,
        stockOptions: 50000,
        benefits: ['五险一金', '年终奖', '股票期权', '免费餐食'],
        totalSalary: 410000,
        currency: 'CNY',
        salaryYear: 2024,
        isAnonymous: true,
        isVerified: false,
        authorId: users[0].id,
        companyId: company.id,
      },
      {
        position: '后端工程师',
        level: 'P7',
        department: '技术部',
        workLocation: '北京',
        workType: 'FULL_TIME',
        experience: 5,
        education: 'MASTER',
        baseSalary: 450000,
        bonus: 90000,
        stockOptions: 80000,
        benefits: ['五险一金', '年终奖', '股票期权', '免费餐食', '健身房'],
        totalSalary: 620000,
        currency: 'CNY',
        salaryYear: 2024,
        isAnonymous: true,
        isVerified: false,
        authorId: users[1] ? users[1].id : users[0].id,
        companyId: company.id,
      },
      {
        position: '产品经理',
        level: 'P6',
        department: '产品部',
        workLocation: '北京',
        workType: 'FULL_TIME',
        experience: 4,
        education: 'BACHELOR',
        baseSalary: 350000,
        bonus: 70000,
        stockOptions: 60000,
        benefits: ['五险一金', '年终奖', '股票期权'],
        totalSalary: 480000,
        currency: 'CNY',
        salaryYear: 2024,
        isAnonymous: true,
        isVerified: false,
        authorId: users[2] ? users[2].id : users[0].id,
        companyId: company.id,
      }
    ]

    for (const salary of salaries) {
      await prisma.salary.create({ data: salary })
      console.log(`  ✅ 创建薪资: ${salary.position} - ${salary.totalSalary}元`)
    }

    // 3. 创建面试经验
    console.log('\n3. 创建面试经验...')
    const interviews = [
      {
        position: '前端工程师',
        department: '技术部',
        interviewType: 'ONSITE',
        interviewRound: 3,
        interviewDate: new Date('2024-06-15'),
        duration: 180,
        difficulty: 'MEDIUM',
        result: 'PASSED',
        rating: 4,
        questions: '1. React Hooks原理\n2. 算法题：两数之和\n3. 项目经验分享\n4. CSS布局问题',
        experience: '面试官很专业，问题有一定难度但不会故意刁难。整个流程比较规范，HR也很友好。',
        tips: '建议提前准备React相关知识，算法题难度中等，主要考察基础。',
        notes: '公司环境不错，团队氛围看起来很好。',
        isAnonymous: true,
        authorId: user.id,
        companyId: company.id,
      },
      {
        position: '后端工程师',
        department: '技术部',
        interviewType: 'ONSITE',
        interviewRound: 4,
        interviewDate: new Date('2024-07-01'),
        duration: 240,
        difficulty: 'HARD',
        result: 'PASSED',
        rating: 5,
        questions: '1. 系统设计：设计一个短链接服务\n2. 算法题：LRU缓存实现\n3. 数据库优化问题\n4. 微服务架构讨论',
        experience: '面试很有挑战性，考察面很广。面试官技术功底很深，能学到很多东西。',
        tips: '需要有扎实的计算机基础，系统设计要多练习，算法题难度较高。',
        notes: '技术氛围很好，面试官都很专业。',
        isAnonymous: true,
        authorId: user.id,
        companyId: company.id,
      }
    ]

    for (const interview of interviews) {
      await prisma.interview.create({ data: interview })
      console.log(`  ✅ 创建面试: ${interview.position} - ${interview.result}`)
    }

    console.log('\n🎉 测试数据创建完成！')
    console.log(`📊 统计:`)
    console.log(`  - 企业评价: ${ratings.length} 条`)
    console.log(`  - 薪资数据: ${salaries.length} 条`)
    console.log(`  - 面试经验: ${interviews.length} 条`)

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n🔚 完成')
  }
}

createCompanyTestData().catch(console.error)
