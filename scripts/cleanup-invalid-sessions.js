const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function cleanupInvalidSessions() {
  console.log('🧹 清理无效的session和account记录...\n')

  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 1. 查找所有session
    const sessions = await prisma.session.findMany({
      select: {
        id: true,
        userId: true,
        expires: true,
      },
    })

    console.log(`📊 找到 ${sessions.length} 个session记录`)

    // 2. 查找所有account
    const accounts = await prisma.account.findMany({
      select: {
        id: true,
        userId: true,
        provider: true,
      },
    })

    console.log(`📊 找到 ${accounts.length} 个account记录`)

    // 3. 查找所有用户
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
      },
    })

    console.log(`📊 找到 ${users.length} 个用户记录`)
    const userIds = new Set(users.map(u => u.id))

    // 4. 找到无效的session（用户不存在）
    const invalidSessions = sessions.filter(s => !userIds.has(s.userId))
    console.log(`❌ 找到 ${invalidSessions.length} 个无效session`)

    // 5. 找到无效的account（用户不存在）
    const invalidAccounts = accounts.filter(a => !userIds.has(a.userId))
    console.log(`❌ 找到 ${invalidAccounts.length} 个无效account`)

    // 6. 删除无效的session
    if (invalidSessions.length > 0) {
      console.log('\n🗑️  删除无效session...')
      for (const session of invalidSessions) {
        console.log(`  删除session: ${session.id} (用户: ${session.userId})`)
        await prisma.session.delete({
          where: { id: session.id }
        })
      }
    }

    // 7. 删除无效的account
    if (invalidAccounts.length > 0) {
      console.log('\n🗑️  删除无效account...')
      for (const account of invalidAccounts) {
        console.log(`  删除account: ${account.id} (用户: ${account.userId}, 提供商: ${account.provider})`)
        await prisma.account.delete({
          where: { id: account.id }
        })
      }
    }

    // 8. 删除过期的session
    const now = new Date()
    const expiredSessions = sessions.filter(s => new Date(s.expires) < now)
    
    if (expiredSessions.length > 0) {
      console.log('\n⏰ 删除过期session...')
      for (const session of expiredSessions) {
        console.log(`  删除过期session: ${session.id} (过期时间: ${session.expires})`)
        await prisma.session.delete({
          where: { id: session.id }
        })
      }
    }

    console.log('\n✅ 清理完成！')
    
    // 9. 显示清理后的统计
    const remainingSessions = await prisma.session.count()
    const remainingAccounts = await prisma.account.count()
    
    console.log(`📊 剩余session: ${remainingSessions}`)
    console.log(`📊 剩余account: ${remainingAccounts}`)

  } catch (error) {
    console.error('❌ 清理过程中出错:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n🔚 清理完成')
  }
}

cleanupInvalidSessions().catch(console.error)
