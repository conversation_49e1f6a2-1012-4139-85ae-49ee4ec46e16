const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function debugUserProfile() {
  console.log('🔍 调试用户资料问题...\n')

  try {
    // 1. 检查数据库连接
    console.log('1. 测试数据库连接...')
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 2. 检查用户表中的数据
    console.log('2. 检查用户表数据...')
    const userCount = await prisma.user.count()
    console.log(`📊 用户总数: ${userCount}`)

    if (userCount === 0) {
      console.log('⚠️  数据库中没有用户数据，这可能是问题的原因')
      console.log('💡 建议：创建一个测试用户或检查数据迁移')
    } else {
      // 显示前几个用户的基本信息
      const users = await prisma.user.findMany({
        take: 5,
        select: {
          id: true,
          email: true,
          name: true,
          createdAt: true,
        },
      })
      
      console.log('👥 用户列表（前5个）:')
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ID: ${user.id}`)
        console.log(`     Email: ${user.email}`)
        console.log(`     Name: ${user.name || '未设置'}`)
        console.log(`     创建时间: ${user.createdAt}`)
        console.log('')
      })
    }

    // 3. 检查账户表
    console.log('3. 检查账户关联数据...')
    const accountCount = await prisma.account.count()
    console.log(`📊 账户关联总数: ${accountCount}`)

    if (accountCount > 0) {
      const accounts = await prisma.account.findMany({
        take: 5,
        select: {
          id: true,
          userId: true,
          provider: true,
          type: true,
        },
      })
      
      console.log('🔗 账户关联列表（前5个）:')
      accounts.forEach((account, index) => {
        console.log(`  ${index + 1}. 用户ID: ${account.userId}`)
        console.log(`     提供商: ${account.provider}`)
        console.log(`     类型: ${account.type}`)
        console.log('')
      })
    }

    // 4. 检查session表
    console.log('4. 检查会话数据...')
    const sessionCount = await prisma.session.count()
    console.log(`📊 会话总数: ${sessionCount}`)

    if (sessionCount > 0) {
      const sessions = await prisma.session.findMany({
        take: 3,
        select: {
          id: true,
          userId: true,
          expires: true,
        },
        orderBy: {
          expires: 'desc',
        },
      })
      
      console.log('🔐 会话列表（最新3个）:')
      sessions.forEach((session, index) => {
        console.log(`  ${index + 1}. 会话ID: ${session.id}`)
        console.log(`     用户ID: ${session.userId}`)
        console.log(`     过期时间: ${session.expires}`)
        console.log(`     是否过期: ${session.expires < new Date() ? '是' : '否'}`)
        console.log('')
      })
    }

    // 5. 测试getUserProfileSafely函数的逻辑
    console.log('5. 测试用户资料查询逻辑...')
    if (userCount > 0) {
      const firstUser = await prisma.user.findFirst({
        select: { id: true }
      })
      
      if (firstUser) {
        console.log(`🧪 测试用户ID: ${firstUser.id}`)
        
        try {
          const userProfile = await prisma.user.findUnique({
            where: { id: firstUser.id },
            select: {
              id: true,
              email: true,
              name: true,
              avatar: true,
              bio: true,
              location: true,
              website: true,
              isVerified: true,
              createdAt: true,
              updatedAt: true,
              _count: {
                select: {
                  posts: true,
                  comments: true,
                  ratings: true,
                  salaries: true,
                  interviews: true,
                },
              },
            },
          })
          
          if (userProfile) {
            console.log('✅ 用户资料查询成功')
            console.log('📋 用户资料数据:')
            console.log(JSON.stringify(userProfile, null, 2))
          } else {
            console.log('❌ 用户资料查询失败 - 用户不存在')
          }
        } catch (error) {
          console.log('❌ 用户资料查询出错:', error.message)
        }
      }
    }

  } catch (error) {
    console.error('❌ 调试过程中出错:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n🔚 调试完成')
  }
}

debugUserProfile().catch(console.error)
