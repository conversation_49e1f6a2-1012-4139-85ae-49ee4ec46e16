const fetch = require('node-fetch')

// 测试发布功能的脚本
async function testPublishFeatures() {
  console.log('🧪 测试发布功能...\n')

  const baseUrl = 'http://localhost:3000'

  try {
    // 1. 测试帖子发布API
    console.log('1. 测试帖子发布API...')
    const postResponse = await fetch(`${baseUrl}/api/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-session-token', // 需要实际的session token
      },
      body: JSON.stringify({
        title: '测试帖子标题',
        content: '这是一个测试帖子的内容，用于验证发布功能是否正常工作。',
        type: 'DISCUSSION',
        category: '技术讨论',
        tags: ['测试', 'API'],
        isAnonymous: false,
      }),
    })

    if (postResponse.ok) {
      const postResult = await postResponse.json()
      console.log('✅ 帖子发布成功:', postResult.data.title)
    } else {
      const error = await postResponse.json()
      console.log('❌ 帖子发布失败:', error.message)
    }

    // 2. 测试面试经验提交API
    console.log('\n2. 测试面试经验提交API...')
    const interviewResponse = await fetch(`${baseUrl}/api/interviews`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-session-token',
      },
      body: JSON.stringify({
        title: '阿里巴巴前端工程师面试经验',
        company: '阿里巴巴',
        position: '前端工程师',
        location: '杭州',
        department: '技术部',
        level: 'P6',
        result: 'PASSED',
        difficulty: 'MEDIUM',
        rating: '4',
        rounds: [
          {
            round: 1,
            type: '技术面试',
            duration: 60,
            questions: 'React相关问题，算法题',
            feedback: '面试官很友好，问题有一定难度'
          }
        ],
        summary: '整体面试体验不错，技术问题比较全面',
        advice: '建议提前准备React和算法相关知识',
        tags: ['前端', 'React', '算法'],
        isAnonymous: true,
      }),
    })

    if (interviewResponse.ok) {
      const interviewResult = await interviewResponse.json()
      console.log('✅ 面试经验提交成功:', interviewResult.data.position)
    } else {
      const error = await interviewResponse.json()
      console.log('❌ 面试经验提交失败:', error.message)
    }

    // 3. 测试薪资数据提交API
    console.log('\n3. 测试薪资数据提交API...')
    const salaryResponse = await fetch(`${baseUrl}/api/salaries`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'next-auth.session-token=your-session-token',
      },
      body: JSON.stringify({
        company: '腾讯',
        position: '后端工程师',
        level: 'T3-1',
        department: '技术部',
        location: '深圳',
        workYears: '3',
        baseSalary: '350000',
        bonus: '80000',
        stockValue: '50000',
        benefits: ['五险一金', '年终奖', '股票期权'],
        description: '工作强度适中，团队氛围不错',
        isAnonymous: true,
      }),
    })

    if (salaryResponse.ok) {
      const salaryResult = await salaryResponse.json()
      console.log('✅ 薪资数据提交成功:', salaryResult.data.position)
    } else {
      const error = await salaryResponse.json()
      console.log('❌ 薪资数据提交失败:', error.message)
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message)
  }

  console.log('\n🔚 测试完成')
}

// 运行测试
testPublishFeatures().catch(console.error)
