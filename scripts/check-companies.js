const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function checkCompanies() {
  console.log('🔍 检查企业数据...\n')

  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功\n')

    // 1. 检查企业总数
    const companyCount = await prisma.company.count()
    console.log(`📊 企业总数: ${companyCount}`)

    if (companyCount === 0) {
      console.log('⚠️  数据库中没有企业数据')
      console.log('💡 建议：运行种子数据脚本创建测试企业')
    } else {
      // 显示前几个企业的基本信息
      const companies = await prisma.company.findMany({
        take: 5,
        select: {
          id: true,
          name: true,
          industry: true,
          size: true,
          isActive: true,
          createdAt: true,
        },
      })
      
      console.log('\n🏢 企业列表（前5个）:')
      companies.forEach((company, index) => {
        console.log(`  ${index + 1}. ${company.name}`)
        console.log(`     ID: ${company.id}`)
        console.log(`     行业: ${company.industry || '未设置'}`)
        console.log(`     规模: ${company.size || '未设置'}`)
        console.log(`     状态: ${company.isActive ? '激活' : '未激活'}`)
        console.log(`     创建时间: ${company.createdAt}`)
        console.log('')
      })

      // 检查相关数据
      console.log('📈 相关数据统计:')
      
      const [ratingsCount, salariesCount, interviewsCount] = await Promise.all([
        prisma.rating.count(),
        prisma.salary.count(),
        prisma.interview.count(),
      ])

      console.log(`  评价数量: ${ratingsCount}`)
      console.log(`  薪资数据: ${salariesCount}`)
      console.log(`  面试经验: ${interviewsCount}`)

      // 如果有企业，测试第一个企业的详情API数据
      if (companies.length > 0) {
        const firstCompany = companies[0]
        console.log(`\n🧪 测试企业详情数据 (${firstCompany.name}):`)
        
        const [ratings, salaries, interviews] = await Promise.all([
          prisma.rating.findMany({
            where: { companyId: firstCompany.id },
            take: 3,
            select: {
              id: true,
              overallRating: true,
              title: true,
              position: true,
              createdAt: true,
            },
          }),
          prisma.salary.findMany({
            where: { companyId: firstCompany.id },
            take: 3,
            select: {
              id: true,
              position: true,
              totalSalary: true,
              createdAt: true,
            },
          }),
          prisma.interview.findMany({
            where: { companyId: firstCompany.id },
            take: 3,
            select: {
              id: true,
              position: true,
              difficulty: true,
              result: true,
              createdAt: true,
            },
          }),
        ])

        console.log(`  该企业评价: ${ratings.length} 条`)
        console.log(`  该企业薪资: ${salaries.length} 条`)
        console.log(`  该企业面试: ${interviews.length} 条`)

        if (ratings.length > 0) {
          console.log('  最新评价:')
          ratings.forEach(r => {
            console.log(`    - ${r.title} (${r.position}) - 评分: ${r.overallRating}`)
          })
        }
      }
    }

  } catch (error) {
    console.error('❌ 检查过程中出错:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n🔚 检查完成')
  }
}

checkCompanies().catch(console.error)
